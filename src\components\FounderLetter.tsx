import React from 'react';
import { motion } from 'framer-motion';
import { Quote, <PERSON>, <PERSON>, <PERSON> } from 'lucide-react';

export function FounderLetter() {
  return (
    <div className="px-4 py-16 bg-primary/5">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <Quote className="w-12 h-12 text-primary mx-auto mb-6" />
          <h2 className="font-display text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Letter from Our Founder
          </h2>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="bg-white rounded-2xl p-8 md:p-12 shadow-xl relative overflow-hidden"
        >
          <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full -translate-y-1/2 translate-x-1/2" />
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-secondary/5 rounded-full translate-y-1/2 -translate-x-1/2" />
          
          <div className="relative">
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed mb-8 font-script">
              Dear Community,
            </p>
            
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed mb-8">
              When we started Vrisham Organic in 2015, we had a simple mission: to make organic farming sustainable for our farmers and organic food accessible to every household. Today, with over 1000+ happy customers and 50+ organic farmers in our community, we're living that mission every day.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-12">
              <div className="bg-primary/5 rounded-xl p-6 text-center">
                <Leaf className="w-8 h-8 text-primary mx-auto mb-3" />
                <h3 className="font-semibold text-gray-800 mb-1">100% Organic</h3>
                <p className="text-gray-600 text-sm">Certified natural farming</p>
              </div>
              <div className="bg-primary/5 rounded-xl p-6 text-center">
                <Heart className="w-8 h-8 text-primary mx-auto mb-3" />
                <h3 className="font-semibold text-gray-800 mb-1">1000+ Customers</h3>
                <p className="text-gray-600 text-sm">Trust our products daily</p>
              </div>
              <div className="bg-primary/5 rounded-xl p-6 text-center">
                <Users className="w-8 h-8 text-primary mx-auto mb-3" />
                <h3 className="font-semibold text-gray-800 mb-1">50+ Farmers</h3>
                <p className="text-gray-600 text-sm">In our growing community</p>
              </div>
            </div>

            <p className="text-lg md:text-xl text-gray-600 leading-relaxed mb-8">
              Our unique pre-order system ensures zero food waste and guarantees our farmers a fair price for their produce. Every vegetable, fruit, and staple in our store is carefully selected, organically grown, and delivered fresh to your doorstep.
            </p>

            <p className="text-lg md:text-xl text-gray-600 leading-relaxed mb-12">
              Thank you for being part of our journey towards a healthier, more sustainable future.
            </p>

            <div className="flex items-center gap-6 border-t border-gray-100 pt-8">
              <img
                src="https://images.unsplash.com/photo-**********-0b93528c311a?auto=format&fit=crop&q=80&w=400"
                alt="Founder"
                className="w-20 h-20 rounded-full object-cover ring-4 ring-primary/10"
              />
              <div>
                <h3 className="font-display text-2xl font-bold text-gray-800 mb-1">
                  Rajesh Kumar
                </h3>
                <p className="text-gray-600">Founder, Vrisham Organic</p>
                <p className="text-primary/80 text-sm mt-1 font-medium">
                  Committed to Organic Farming Since 2015
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}