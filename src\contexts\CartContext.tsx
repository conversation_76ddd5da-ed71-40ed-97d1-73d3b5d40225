import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Coupon } from '../firebase/schema';

export interface CartItem {
  id: string;
  name: string;
  nameTamil: string;
  image: string;
  price: number;
  quantity: number;
  unit: string;
  status: 'pending' | 'confirmed';
  selectedSize?: {
    id: string;
    label: string;
    weightRange: string;
    price?: number;
  };
  type?: 'pre-order' | 'in-stock';
}

interface CartContextType {
  items: CartItem[];
  addToCart: (item: CartItem) => void;
  removeFromCart: (id: string) => void;
  updateQuantity: (id: string, change: number) => void;
  clearCart: () => void;
  isInCart: (id: string) => boolean;
  getItemQuantity: (id: string) => number;
  subtotal: number;
  deliveryFee: number;
  total: number;
  // Coupon functionality
  appliedCoupon: Coupon | null;
  couponDiscount: number;
  applyCoupon: (coupon: Coupon, discountAmount: number) => void;
  removeCoupon: () => void;
  // Delivery functionality
  setDeliveryFee: (fee: number) => void;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export const CartProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [items, setItems] = useState<CartItem[]>([]);
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);
  const [couponDiscount, setCouponDiscount] = useState<number>(0);
  const [deliveryFee, setDeliveryFee] = useState<number>(0);

  const addToCart = (item: CartItem) => {
    setItems(prevItems => {
      // Check if item already exists in cart
      const existingItemIndex = prevItems.findIndex(i =>
        i.id === item.id &&
        ((!i.selectedSize && !item.selectedSize) ||
         (i.selectedSize?.id === item.selectedSize?.id))
      );

      if (existingItemIndex >= 0) {
        // Update quantity if item exists
        const updatedItems = [...prevItems];
        updatedItems[existingItemIndex].quantity += item.quantity;
        return updatedItems;
      } else {
        // Add new item
        return [...prevItems, item];
      }
    });
  };

  const removeFromCart = (id: string) => {
    setItems(prevItems => prevItems.filter(item => item.id !== id));
  };

  const updateQuantity = (id: string, change: number) => {
    setItems(prevItems =>
      prevItems.map(item => {
        if (item.id === id) {
          const newQuantity = item.quantity + change;
          if (newQuantity > 0) {
            return { ...item, quantity: newQuantity };
          }
          // If quantity would be 0 or less, filter this item out later
          return { ...item, quantity: 0 };
        }
        return item;
      }).filter(item => item.quantity > 0) // Remove items with 0 quantity
    );
  };

  const clearCart = () => {
    setItems([]);
    setAppliedCoupon(null);
    setCouponDiscount(0);
  };

  const isInCart = (id: string) => {
    return items.some(item => item.id === id);
  };

  const getItemQuantity = (id: string) => {
    const item = items.find(item => item.id === id);
    return item ? item.quantity : 0;
  };

  const applyCouponToCart = (coupon: Coupon, discountAmount: number) => {
    setAppliedCoupon(coupon);
    setCouponDiscount(discountAmount);
  };

  const removeCoupon = () => {
    setAppliedCoupon(null);
    setCouponDiscount(0);
  };

  const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const total = subtotal + deliveryFee - couponDiscount;

  return (
    <CartContext.Provider value={{
      items,
      addToCart,
      removeFromCart,
      updateQuantity,
      clearCart,
      isInCart,
      getItemQuantity,
      subtotal,
      deliveryFee,
      total,
      appliedCoupon,
      couponDiscount,
      applyCoupon: applyCouponToCart,
      removeCoupon,
      setDeliveryFee
    }}>
      {children}
    </CartContext.Provider>
  );
};
